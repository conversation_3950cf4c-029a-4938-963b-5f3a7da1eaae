# Task ID: 29
# Title: 前端：实现基于角色的路由保护
# Status: pending
# Dependencies: 28
# Priority: high
# Description: 使用Vue Router的导航守卫，根据用户角色限制对特定页面的访问，例如只有管理员才能访问用户管理页面。
# Details:
1. 在`router/index.js`中，为需要权限的路由添加`meta: { requiredRole: 'admin' }`等元信息。 2. 使用`router.beforeEach`全局导航守卫。 3. 在守卫中，获取用户角色（例如从Pinia/Vuex store中）。 4. 比较用户角色与路由元信息中的`requiredRole`，如果不匹配则重定向到登录页或403页面。

# Test Strategy:
1. 以“员工”角色登录，尝试访问管理员页面，验证是否被重定向。 2. 以“管理员”角色登录，验证是否可以成功访问管理员页面。
