# Task ID: 35
# Title: 前端：开发用户头像上传UI
# Status: pending
# Dependencies: None
# Priority: medium
# Description: 在用户个人资料页面，创建用于更换头像的UI，包括当前头像显示、上传按钮和新头像预览。
# Details:
1. 创建或定位到用户个人资料页面组件。 2. 添加一个`<input type="file" accept="image/png, image/jpeg">`并将其隐藏。 3. 添加一个“上传头像”按钮，点击时触发文件输入框。 4. 监听文件输入框的`change`事件，使用`FileReader`读取用户选择的图片文件，并将其显示在一个预览`<img>`标签中。

# Test Strategy:
1. 访问个人资料页。 2. 点击上传按钮并选择一个本地的JPG或PNG图片。 3. 验证选择的图片是否成功显示在预览区域。
