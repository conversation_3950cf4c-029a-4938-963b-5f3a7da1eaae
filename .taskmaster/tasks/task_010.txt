# Task ID: 10
# Title: Frontend: 'My Contracts' Tab Page for Employees
# Status: pending
# Dependencies: 8, 9
# Priority: medium
# Description: Build the UI for employees to view a list of their submitted contracts and their current statuses.
# Details:
Create 'src/components/tabs/ContractsPage.vue'. Use an 'el-table' to display the contract list fetched from '/api/contracts/my'. Columns should include Serial Number, Status, Submitter, and Submission Date. Implement client-side pagination connected to the table component.

# Test Strategy:
Log in as an employee who has submitted contracts. Open the 'My Contracts' tab and verify the table is populated correctly. Test the pagination controls.
