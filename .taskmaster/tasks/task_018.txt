# Task ID: 18
# Title: Frontend: Admin User Management Page
# Status: pending
# Dependencies: 5, 17
# Priority: medium
# Description: Build the UI for administrators to perform user management operations.
# Details:
Create 'src/components/tabs/UserManagePage.vue'. Use an 'el-table' to list users, with buttons for 'Edit', 'Delete', 'Reset Password', and 'Ban/Unban'. Use 'el-dialog' or 'el-drawer' for the create/edit forms.

# Test Strategy:
Log in as admin. Open the 'User Management' tab. Create a new user, edit their role, ban them, and then delete them. Verify the table updates correctly after each action.
