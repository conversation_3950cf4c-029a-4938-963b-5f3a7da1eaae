# Task ID: 4
# Title: Frontend: API Client and Login Page
# Status: pending
# Dependencies: 2, 3
# Priority: high
# Description: Create a reusable Axios instance for API calls with request/response interceptors for token/session management. Build the UI and logic for the user login page.
# Details:
Create 'src/api/request.js' to handle base URL and auth headers. Build 'src/views/Login.vue' using Element Plus form components. On successful login, store user info/token and redirect to the main layout.

# Test Strategy:
Test the login form with correct and incorrect credentials. Verify that after a successful login, the user is redirected and the session/token is stored in the browser.
