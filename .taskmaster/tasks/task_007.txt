# Task ID: 7
# Title: Backend: Contract Submission API
# Status: pending
# Dependencies: 2, 6
# Priority: high
# Description: Develop the API endpoint for employees to submit a new contract.
# Details:
Create 'POST /api/contracts'. This endpoint will receive file info, reviewer ID, and notes. It must generate a unique, sequential serial number (e.g., 'HT001', 'HT002') and create a new record in the 'contracts' table with 'pending' status.

# Test Strategy:
Call the endpoint with valid data. Check the database to confirm a new contract record is created with the correct details, a unique serial number, and 'pending' status.
