# Task ID: 39
# Title: 前端：开发审核分布图表
# Status: pending
# Dependencies: 38
# Priority: low
# Description: 在审核统计页面，利用图表库开发审核分布图，用于按合同状态或类型展示审核分布情况。
# Details:
1. 复用已集成的ECharts库。 2. 创建一个新的API端点`GET /api/statistics/distribution`，返回按状态（待审核、已通过、已驳回）和类型分组的合同数量。 3. 在统计页面组件中调用此API。 4. 将返回的数据配置为ECharts的option，渲染一个饼图或环形图。

# Test Strategy:
1. 访问审核统计页面。 2. 验证“审核分布图”是否已成功渲染。 3. 检查饼图的各个扇区是否正确代表了不同的审核状态/类型，以及其数值和标签是否准确。
