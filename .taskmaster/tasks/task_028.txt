# Task ID: 28
# Title: 后端：定义四级用户角色体系
# Status: pending
# Dependencies: None
# Priority: high
# Description: 在后端数据库和应用层中，设计并实现“员工”、“县局审核员”、“市局审核员”、“管理员”四级角色体系。
# Details:
1. 设计或修改数据库`roles`表和`user_roles`关联表。 2. 在`roles`表中插入四种角色的记录。 3. 创建基础的API端点（如 `GET /api/roles`）以供前端调用。 4. 在用户认证和信息获取的API中，确保返回当前用户的角色信息。

# Test Strategy:
1. 使用API测试工具（如Postman）请求用户信息的接口。 2. 验证返回的数据中是否包含正确的角色字段（如`role: 'employee'`）。 3. 检查数据库，确认角色数据已正确创建。
