{"master": {"tasks": [{"id": 1, "title": "Backend: Project Scaffolding and Database Setup", "description": "Initialize the Node.js/Express backend project, create the folder structure (routes, models, middleware), and set up the SQLite database with 'users' and 'contracts' tables.", "details": "Use Express.js for the server. Define the schema for 'users' and 'contracts' as specified in the PRD. Create an initialization script (e.g., 'npm run init-db') to create the tables and insert the default 'admin' user with a hashed password.", "testStrategy": "Run the init script and verify the 'database.sqlite' file is created with the correct tables and the admin user exists.", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 2, "title": "Backend: Authentication API and Middleware", "description": "Implement user authentication endpoints for login, logout, and profile retrieval. Create middleware to protect routes based on user roles.", "details": "Endpoints: POST /api/auth/login, POST /api/auth/logout, GET /api/auth/profile. Use a simple session-based mechanism. The middleware should check for a valid session and user role ('employee', 'reviewer', 'admin') for protected routes.", "testStrategy": "Use an API client like Postman to test login with valid/invalid credentials, access a protected route with/without a session, and check role-based access.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "Frontend: Project Setup and UI Framework Integration", "description": "Initialize the Vue 3 project using Vite. Install and configure essential libraries: Element Plus for UI, Tailwind CSS for styling, and vue-router for navigation.", "details": "Run 'npm create vite@latest' with the Vue template. Follow installation guides for Element Plus and Tailwind CSS. Set up a basic router configuration in 'src/router/index.js'.", "testStrategy": "Run 'npm run dev' and ensure the application loads without errors, showing a basic page with an Element Plus component rendered correctly.", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 4, "title": "Frontend: API Client and Login Page", "description": "Create a reusable Axios instance for API calls with request/response interceptors for token/session management. Build the UI and logic for the user login page.", "details": "Create 'src/api/request.js' to handle base URL and auth headers. Build 'src/views/Login.vue' using Element Plus form components. On successful login, store user info/token and redirect to the main layout.", "testStrategy": "Test the login form with correct and incorrect credentials. Verify that after a successful login, the user is redirected and the session/token is stored in the browser.", "priority": "high", "dependencies": [2, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Frontend: Main Layout with Sidebar and Tab Management", "description": "Implement the main application layout featuring a left sidebar for navigation and a dynamic tabbed interface for content, as described in the PRD.", "details": "Create a 'Layout.vue' component using Element Plus's Container components. The sidebar menu ('el-menu') should be role-based. Implement a 'useTabs' composable to manage opening, closing, and switching between tabs.", "testStrategy": "Log in as different roles and verify the sidebar menu items change accordingly. Click menu items to open new tabs and ensure they can be closed (except for the 'Home' tab).", "priority": "high", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 6, "title": "Backend: File Upload and Serving Endpoints", "description": "Create API endpoints to handle PDF file uploads and to serve/download stored files.", "details": "Use 'multer' for handling 'multipart/form-data'. Implement 'POST /api/files/upload' to save files to the 'backend/uploads/' directory with a 10MB size limit. Create 'GET /api/files/:id/preview' to serve files with the correct content type for in-browser viewing.", "testStrategy": "Upload a PDF file via an API client. Verify the file is saved on the server. Access the preview URL to ensure the PDF is displayed in the browser.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 7, "title": "Backend: Contract Submission API", "description": "Develop the API endpoint for employees to submit a new contract.", "details": "Create 'POST /api/contracts'. This endpoint will receive file info, reviewer ID, and notes. It must generate a unique, sequential serial number (e.g., 'HT001', 'HT002') and create a new record in the 'contracts' table with 'pending' status.", "testStrategy": "Call the endpoint with valid data. Check the database to confirm a new contract record is created with the correct details, a unique serial number, and 'pending' status.", "priority": "high", "dependencies": [2, 6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Frontend: Contract Submission Tab Page", "description": "Create the 'Submit Contract' tab page for employees to upload a PDF and submit it for review.", "details": "Create 'src/components/tabs/SubmitPage.vue'. Use Element Plus 'el-upload' for file upload (calling the file upload API first) and an 'el-form' for selecting a reviewer from a pre-set list. On submit, call the contract submission API.", "testStrategy": "As an employee, open the 'Submit Contract' tab, upload a PDF, select a reviewer, and submit. Verify a success message with the new serial number is shown.", "priority": "high", "dependencies": [5, 7], "status": "pending", "subtasks": []}, {"id": 9, "title": "Backend: Contract List APIs for All Roles", "description": "Implement various GET endpoints to fetch lists of contracts tailored to different user roles, with pagination.", "details": "Endpoints: 'GET /api/contracts/my' for employees, 'GET /api/contracts/pending' and 'GET /api/contracts/reviewed' for reviewers. All endpoints must support 'page' and 'size' query parameters for pagination.", "testStrategy": "Call each endpoint as the appropriate user role. Test pagination parameters ('page', 'size') and verify the response structure contains 'contracts', 'total', 'page', and 'size'.", "priority": "medium", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 10, "title": "Frontend: 'My Contracts' <PERSON><PERSON> Page for Employees", "description": "Build the UI for employees to view a list of their submitted contracts and their current statuses.", "details": "Create 'src/components/tabs/ContractsPage.vue'. Use an 'el-table' to display the contract list fetched from '/api/contracts/my'. Columns should include Serial Number, Status, Submitter, and Submission Date. Implement client-side pagination connected to the table component.", "testStrategy": "Log in as an employee who has submitted contracts. Open the 'My Contracts' tab and verify the table is populated correctly. Test the pagination controls.", "priority": "medium", "dependencies": [8, 9], "status": "pending", "subtasks": []}, {"id": 11, "title": "Backend: Contract Detail and Review Action APIs", "description": "Create APIs to fetch a single contract's details and to perform review actions (start review, approve, reject).", "details": "Endpoints: 'GET /api/contracts/:id' to get full details. 'PUT /api/contracts/:id/start-review' to change status to 'reviewing'. 'PUT /api/contracts/:id/review' to set status to 'approved' or 'rejected' and save the review comment.", "testStrategy": "Fetch a contract by ID. Call the 'start-review' endpoint and verify status change. Call the 'review' endpoint with 'approved'/'rejected' and check that status and comment are updated in the database.", "priority": "high", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 12, "title": "Frontend: Reusable PDF Viewer Component", "description": "Create a dedicated component to embed and display PDF files fetched from the backend.", "details": "Create 'src/components/common/PdfViewer.vue' using the 'vue-pdf-embed' library. The component should accept a file URL as a prop and render the PDF.", "testStrategy": "Pass a valid PDF URL from the backend to the component and verify it renders correctly within a page.", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 13, "title": "Frontend: Reviewer's 'Pending Review' Tab Page", "description": "Create the 'Pending Review' tab for reviewers, listing all contracts awaiting their review.", "details": "Create 'src/components/tabs/ReviewPage.vue'. Fetch data from '/api/contracts/pending' and display it in an 'el-table'. Each row should be clickable to open a detailed view in a new tab.", "testStrategy": "Log in as a reviewer. Verify the 'Pending Review' tab shows a list of contracts assigned to them. Clicking a row should open a new 'Contract Detail' tab.", "priority": "high", "dependencies": [9, 5], "status": "pending", "subtasks": []}, {"id": 14, "title": "Frontend: Contract Detail and Review Action UI", "description": "Build the contract detail tab, which shows contract information, the PDF preview, and the review action form.", "details": "Create 'src/components/tabs/ContractDetailPage.vue'. This dynamic tab should fetch data from '/api/contracts/:id', display metadata, embed the 'PdfViewer.vue' component, and show a form for reviewers to approve/reject with comments. The form should only be visible to the assigned reviewer.", "testStrategy": "As a reviewer, open a contract detail tab. Verify all information and the PDF are displayed. Submit an approval/rejection and confirm the UI updates and the tab can be closed.", "priority": "high", "dependencies": [11, 12, 13], "status": "pending", "subtasks": []}, {"id": 15, "title": "Backend: Contract Modification API", "description": "Implement the API to allow an employee to modify a contract that is in a 'pending' or 'rejected' state.", "details": "Create 'PUT /api/contracts/:id'. This endpoint should validate that the user is the original submitter and the contract status is 'pending' or 'rejected'. It allows updating the associated file and notes, but keeps the same serial number. The status should be reset to 'pending' if it was 'rejected'.", "testStrategy": "Attempt to update a contract in 'reviewing' state (should fail). Attempt to update a 'rejected' contract with a new file (should succeed). Verify the file path is updated and status is 'pending' in the DB.", "priority": "medium", "dependencies": [11], "status": "pending", "subtasks": []}, {"id": 16, "title": "Frontend: Contract Modification and Re-submission Flow", "description": "Enable the UI for employees to modify and re-submit a rejected contract.", "details": "In the 'My Contracts' table ('ContractsPage.vue'), show a 'Modify' button for contracts with 'rejected' or 'pending' status. Clicking it should open a view similar to the submission page, pre-filled with existing data, allowing the user to upload a new PDF and re-submit.", "testStrategy": "As an employee, find a rejected contract in 'My Contracts'. Click 'Modify', upload a new file, and re-submit. Verify the contract status changes to 'pending' and it appears in the reviewer's queue again.", "priority": "medium", "dependencies": [10, 15], "status": "pending", "subtasks": []}, {"id": 17, "title": "Backend: Admin User Management APIs", "description": "Implement the full suite of RESTful APIs for administrators to manage users.", "details": "Create all endpoints under '/api/admin/users': GET for listing/searching, POST for creating (default password '123456'), PUT for updating role/status, DELETE for soft-deleting, and a specific PUT for resetting passwords.", "testStrategy": "As an admin, use an API client to test creating a new user, listing all users, changing a user's role, banning a user, and resetting a password. Verify DB changes.", "priority": "medium", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 18, "title": "Frontend: Admin User Management Page", "description": "Build the UI for administrators to perform user management operations.", "details": "Create 'src/components/tabs/UserManagePage.vue'. Use an 'el-table' to list users, with buttons for 'Edit', 'Delete', 'Reset Password', and 'Ban/Unban'. Use 'el-dialog' or 'el-drawer' for the create/edit forms.", "testStrategy": "Log in as admin. Open the 'User Management' tab. Create a new user, edit their role, ban them, and then delete them. Verify the table updates correctly after each action.", "priority": "medium", "dependencies": [5, 17], "status": "pending", "subtasks": []}, {"id": 19, "title": "Backend: Admin System Monitoring APIs", "description": "Create APIs for the admin to view all contracts and get simple system statistics.", "details": "Endpoints: 'GET /api/admin/contracts' to list all contracts in the system with pagination and filtering. 'GET /api/admin/statistics' to return simple counts like total users, total contracts, etc.", "testStrategy": "As an admin, call the endpoints and verify they return all data, not just data related to the admin user. Check that the statistics are accurate.", "priority": "low", "dependencies": [9, 17], "status": "pending", "subtasks": []}, {"id": 20, "title": "Frontend: Admin Dashboard and Contract List Pages", "description": "Create the UI tabs for the administrator to monitor the system.", "details": "Create a 'HomePage.vue' for the admin role that displays the simple stats from the statistics API. Create a separate admin-only tab to display the list of all contracts, reusing the contract table component.", "testStrategy": "Log in as admin. Verify the dashboard shows correct numbers. Go to the all contracts list and verify it shows contracts from all users.", "priority": "low", "dependencies": [18, 19], "status": "pending", "subtasks": []}, {"id": 21, "title": "Frontend: Role-based Menu and Route Guards", "description": "Finalize the role-based access control on the frontend, ensuring menus and routes are correctly restricted.", "details": "Use 'vue-router' navigation guards ('beforeEach') to check user role from the auth store before allowing access to a route. Dynamically render the sidebar menu items based on the user's role.", "testStrategy": "Log in as each role (employee, reviewer, admin). Verify the sidebar menu is correct for each. Attempt to manually navigate to a restricted URL (e.g., employee trying to access '/admin/users') and confirm they are blocked or redirected.", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 22, "title": "Composables: Create Reusable Logic for Data Fetching", "description": "Refactor data fetching logic into reusable Vue 3 composables, such as 'useContracts' and 'useTable'.", "details": "Create 'src/composables/useContracts.js' to encapsulate logic for fetching, submitting, and updating contracts. Create 'src/composables/useTable.js' for generic table state management (loading, pagination). Refactor existing components to use these composables.", "testStrategy": "Verify that pages using these composables (e.g., 'My Contracts', 'Pending Review') still function identically to before the refactor. Check for cleaner component code.", "priority": "low", "dependencies": [10, 13], "status": "pending", "subtasks": []}, {"id": 23, "title": "System-wide: <PERSON><PERSON><PERSON> and User Feedback", "description": "Implement consistent error handling and user feedback mechanisms across the application.", "details": "Use the Axios response interceptor to catch API errors. Display user-friendly error messages using Element Plus's 'ElMessage' or 'ElNotification' components for events like failed uploads, invalid logins, or permission errors.", "testStrategy": "Intentionally trigger various errors: submit a form with invalid data, try to access a resource without permission, disconnect the backend server. Verify that clear, non-technical error messages are shown to the user.", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 24, "title": "Documentation: Create a Basic README", "description": "Write a README.md file for the project root, detailing the project setup, installation, and how to run the application.", "details": "Include sections for: Project Overview, Tech Stack, Environment Requirements (Node.js version), Installation Steps (backend and frontend), and Default User Credentials.", "testStrategy": "Ask a new person to follow the README to set up and run the project. They should be able to do so without further instruction.", "priority": "low", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 25, "title": "Final E2E Testing and Bug Fixing", "description": "Conduct a full end-to-end test of the primary user flows and fix any identified bugs before demo.", "details": "Test the complete lifecycle: 1. <PERSON><PERSON> creates employee/reviewer accounts. 2. Employee logs in, submits a contract. 3. Reviewer logs in, finds the contract, reviews it, and rejects it. 4. Employee logs in, modifies the rejected contract, and re-submits. 5. Reviewer approves the contract. 6. Verify final status and history.", "testStrategy": "Successfully complete the entire user flow described in the details without any critical errors. Check the database at each step to ensure data integrity.", "priority": "high", "dependencies": [16, 18, 20, 21, 23], "status": "pending", "subtasks": []}, {"id": 26, "title": "登录页面品牌化：更新标题与Logo", "description": "更新登录页面以体现“宿迁烟草合同审核系统”品牌，包括更改HTML页面标题和集成官方Logo。", "details": "1. 定位到登录页面的Vue组件。 2. 修改`document.title`或使用Vue Meta等插件将标题更新为“宿迁烟草合同审核系统”。 3. 在`assets`目录中找到或放置`logo.png`。 4. 在组件模板中，使用`<img>`标签替换现有Logo占位符，并确保路径正确。", "testStrategy": "1. 打开应用的登录页面。 2. 验证浏览器标签页显示的标题是否为“宿迁烟草合同审核系统”。 3. 确认宿迁烟草的新Logo已正确显示，无拉伸或变形。", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 27, "title": "登录页面品牌化：优化视觉设计", "description": "应用宿迁烟草的品牌色彩方案，并优化登录页面的整体布局，提升专业感和品牌形象。", "details": "1. 在项目的CSS或SCSS文件中定义品牌主色调和辅助色。 2. 将新颜色应用到按钮、背景、链接等元素上。 3. 调整登录表单、Logo和背景图的布局，确保在不同分辨率下都具有良好的视觉对齐和间距。 4. 确保设计具有响应式能力。", "testStrategy": "1. 在桌面和移动设备上加载登录页面。 2. 验证新的品牌色是否已正确应用。 3. 检查是否存在任何UI布局问题，如元素重叠、对齐错误等。", "priority": "medium", "dependencies": [26], "status": "pending", "subtasks": []}, {"id": 28, "title": "后端：定义四级用户角色体系", "description": "在后端数据库和应用层中，设计并实现“员工”、“县局审核员”、“市局审核员”、“管理员”四级角色体系。", "details": "1. 设计或修改数据库`roles`表和`user_roles`关联表。 2. 在`roles`表中插入四种角色的记录。 3. 创建基础的API端点（如 `GET /api/roles`）以供前端调用。 4. 在用户认证和信息获取的API中，确保返回当前用户的角色信息。", "testStrategy": "1. 使用API测试工具（如Postman）请求用户信息的接口。 2. 验证返回的数据中是否包含正确的角色字段（如`role: 'employee'`）。 3. 检查数据库，确认角色数据已正确创建。", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 29, "title": "前端：实现基于角色的路由保护", "description": "使用Vue Router的导航守卫，根据用户角色限制对特定页面的访问，例如只有管理员才能访问用户管理页面。", "details": "1. 在`router/index.js`中，为需要权限的路由添加`meta: { requiredRole: 'admin' }`等元信息。 2. 使用`router.beforeEach`全局导航守卫。 3. 在守卫中，获取用户角色（例如从Pinia/Vuex store中）。 4. 比较用户角色与路由元信息中的`requiredRole`，如果不匹配则重定向到登录页或403页面。", "testStrategy": "1. 以“员工”角色登录，尝试访问管理员页面，验证是否被重定向。 2. 以“管理员”角色登录，验证是否可以成功访问管理员页面。", "priority": "high", "dependencies": [28], "status": "pending", "subtasks": []}, {"id": 30, "title": "前端：开发合同上传时的审核员选择功能", "description": "为“员工”角色在合同上传页面添加一个下拉选择框，允许其选择将合同提交给县局或市局审核员。", "details": "1. 在合同上传的Vue组件中，添加一个`<select>`或UI库的等效组件。 2. 创建一个新的API端点 `GET /api/reviewers`，用于返回所有县局和市局审核员的列表。 3. 在组件加载时调用此API，并用返回的数据填充下拉框。 4. 将用户选择的审核员ID包含在合同提交的请求载荷中。", "testStrategy": "1. 以“员工”角色登录并进入合同上传页面。 2. 验证审核员选择框是否存在并且已填充了审核员列表。 3. 选择一个审核员并提交合同，使用浏览器开发者工具检查网络请求，确认请求体中包含了正确的`reviewerId`。", "priority": "medium", "dependencies": [28], "status": "pending", "subtasks": []}, {"id": 31, "title": "前端：开发右上角通知中心UI框架", "description": "在应用顶部的导航栏中，创建一个通知图标（铃铛）和点击后展开的通知列表面板。", "details": "1. 在主布局组件（如`App.vue`或`Header.vue`）中添加一个铃铛图标。 2. 为图标绑定点击事件，用于切换通知面板的显示/隐藏。 3. 创建`NotificationPanel.vue`组件，用于显示通知列表。 4. 初期使用静态的模拟数据填充通知列表，每条通知包含标题、时间和未读状态。", "testStrategy": "1. 点击页面右上角的铃铛图标，验证通知面板是否能正常显示和隐藏。 2. 检查面板中的模拟通知数据是否按设计稿正确渲染。", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 32, "title": "前端：集成PDF.js实现PDF预览缩放功能", "description": "集成`PDF.js`或其Vue封装库，为合同预览功能增加“放大”、“缩小”和“适应页面”的控制按钮。", "details": "1. 安装`pdfjs-dist`或`vue-pdf-embed`等库。 2. 创建一个`PdfViewer.vue`组件，用于渲染PDF文档。 3. 在组件中添加三个按钮：放大、缩小、适应页面。 4. 将按钮的点击事件分别绑定到PDF.js库提供的缩放控制API上。", "testStrategy": "1. 打开一个合同的预览页面。 2. 点击“放大”和“缩小”按钮，验证PDF视图是否相应地缩放。 3. 点击“适应页面”按钮，验证PDF页面是否完整地显示在可视区域内。", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 33, "title": "前端：为PDF预览增加全屏和翻页功能", "description": "在新的PDF预览组件上，增加全屏预览模式和页面导航（上一页、下一页、页码跳转）功能。", "details": "1. 添加一个“全屏”按钮，调用浏览器的Fullscreen API使预览组件全屏化。 2. 添加“上一页”和“下一页”按钮，并调用PDF库的翻页函数。 3. 添加一个页码输入框和当前页/总页数显示，允许用户直接跳转到指定页面。", "testStrategy": "1. 点击“全屏”按钮，验证预览器是否充满整个屏幕，按ESC键可退出。 2. 对于多页PDF，使用翻页按钮进行导航。 3. 在页码输入框中输入一个页码，验证是否能正确跳转。", "priority": "medium", "dependencies": [32], "status": "pending", "subtasks": []}, {"id": 34, "title": "前端：实现合同打印功能", "description": "在合同预览页面添加一个打印按钮，允许用户调用浏览器打印功能来打印当前合同。", "details": "1. 在PDF预览界面添加一个“打印”按钮。 2. 为按钮绑定点击事件，调用`window.print()`方法。 3. 使用`@media print` CSS媒体查询，编写打印样式表，隐藏所有非合同内容的UI元素（如导航栏、按钮、侧边栏等）。", "testStrategy": "1. 点击“打印”按钮，调出浏览器的打印预览。 2. 在预览中，验证除了合同内容本身，其他所有网站UI元素都已被隐藏。 3. 确认打印布局正常，没有内容被截断。", "priority": "medium", "dependencies": [32], "status": "pending", "subtasks": []}, {"id": 35, "title": "前端：开发用户头像上传UI", "description": "在用户个人资料页面，创建用于更换头像的UI，包括当前头像显示、上传按钮和新头像预览。", "details": "1. 创建或定位到用户个人资料页面组件。 2. 添加一个`<input type=\"file\" accept=\"image/png, image/jpeg\">`并将其隐藏。 3. 添加一个“上传头像”按钮，点击时触发文件输入框。 4. 监听文件输入框的`change`事件，使用`FileReader`读取用户选择的图片文件，并将其显示在一个预览`<img>`标签中。", "testStrategy": "1. 访问个人资料页。 2. 点击上传按钮并选择一个本地的JPG或PNG图片。 3. 验证选择的图片是否成功显示在预览区域。", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 36, "title": "前端：集成头像裁剪功能", "description": "在用户上传新头像后，集成一个图片裁剪工具，允许用户在保存前对头像进行裁剪和调整。", "details": "1. 安装一个Vue的图片裁剪库，如`vue-cropperjs`。 2. 当用户选择图片后（承接上一个任务），将图片加载到裁剪组件中。 3. 用户调整裁剪框后，提供一个“保存”按钮。 4. 点击“保存”时，从裁剪库获取裁剪后的图片数据（Base64或Blob格式），准备发送到后端。", "testStrategy": "1. 上传一张图片后，验证裁剪工具是否出现。 2. 调整裁剪框的大小和位置。 3. 点击保存，并通过`console.log`验证是否能获取到裁剪后的图片数据。", "priority": "low", "dependencies": [35], "status": "pending", "subtasks": []}, {"id": 37, "title": "前端：优化首页快捷操作按钮", "description": "确保首页的所有快捷操作按钮（如“发起合同”、“我的审核”等）都功能正常，并能正确跳转到对应的功能页面。", "details": "1. 审查首页Vue组件的模板代码。 2. 检查每个快捷操作按钮，确保其使用了`<router-link :to=\"...\">`或绑定了`@click=\"router.push(...)\"`事件。 3. 确认所有目标路由都已在`router/index.js`中正确定义。", "testStrategy": "1. 访问应用首页。 2. 依次点击每一个快捷操作按钮。 3. 验证每次点击后，应用是否都导航到了预期的功能页面。", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 38, "title": "前端：集成图表库并创建审核趋势图", "description": "在审核统计页面，集成一个可视化图表库（如ECharts），并开发第一个图表：审核趋势图。", "details": "1. 安装`echarts`和`vue-echarts`。 2. 在统计页面组件中，引入并注册ECharts组件。 3. 创建一个API端点`GET /api/statistics/trends`，返回按时间（日/周/月）聚合的审核数量。 4. 在组件中调用此API，并将返回的数据配置为ECharts的option，渲染一个折线图或柱状图。", "testStrategy": "1. 访问审核统计页面。 2. 验证“审核趋势图”是否已成功渲染。 3. 检查图表的X轴（时间）和Y轴（数量）是否正确，数据点是否与API返回的模拟数据一致。", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 39, "title": "前端：开发审核分布图表", "description": "在审核统计页面，利用图表库开发审核分布图，用于按合同状态或类型展示审核分布情况。", "details": "1. 复用已集成的ECharts库。 2. 创建一个新的API端点`GET /api/statistics/distribution`，返回按状态（待审核、已通过、已驳回）和类型分组的合同数量。 3. 在统计页面组件中调用此API。 4. 将返回的数据配置为ECharts的option，渲染一个饼图或环形图。", "testStrategy": "1. 访问审核统计页面。 2. 验证“审核分布图”是否已成功渲染。 3. 检查饼图的各个扇区是否正确代表了不同的审核状态/类型，以及其数值和标签是否准确。", "priority": "low", "dependencies": [38], "status": "pending", "subtasks": []}, {"id": 40, "title": "管理员功能：开发用户管理页面基础框架", "description": "为管理员角色创建一个用户管理页面，能够以列表形式展示系统中的所有用户及其角色。", "details": "1. 创建一个新的`UserManagement.vue`页面组件。 2. 在该组件中，调用`GET /api/users`接口获取所有用户数据。 3. 使用表格（Table）组件将用户数据渲染出来，至少包含用户名、角色、创建时间等列。 4. 确保此页面受路由守卫保护，仅管理员可访问。", "testStrategy": "1. 以管理员身份登录，导航到用户管理页面。 2. 验证用户列表是否正确显示，并且数据与后端一致。 3. 尝试以非管理员身份访问该页面URL，验证是否被拒绝访问。", "priority": "medium", "dependencies": [29], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-22T04:39:16.167Z", "updated": "2025-07-22T05:25:38.678Z", "description": "Tasks for master context"}}}