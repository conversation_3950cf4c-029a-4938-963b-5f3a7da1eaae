# Task ID: 6
# Title: Backend: File Upload and Serving Endpoints
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Create API endpoints to handle PDF file uploads and to serve/download stored files.
# Details:
Use 'multer' for handling 'multipart/form-data'. Implement 'POST /api/files/upload' to save files to the 'backend/uploads/' directory with a 10MB size limit. Create 'GET /api/files/:id/preview' to serve files with the correct content type for in-browser viewing.

# Test Strategy:
Upload a PDF file via an API client. Verify the file is saved on the server. Access the preview URL to ensure the PDF is displayed in the browser.
