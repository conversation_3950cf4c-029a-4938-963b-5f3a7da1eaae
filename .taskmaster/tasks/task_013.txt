# Task ID: 13
# Title: Frontend: Reviewer's 'Pending Review' Tab Page
# Status: pending
# Dependencies: 9, 5
# Priority: high
# Description: Create the 'Pending Review' tab for reviewers, listing all contracts awaiting their review.
# Details:
Create 'src/components/tabs/ReviewPage.vue'. Fetch data from '/api/contracts/pending' and display it in an 'el-table'. Each row should be clickable to open a detailed view in a new tab.

# Test Strategy:
Log in as a reviewer. Verify the 'Pending Review' tab shows a list of contracts assigned to them. Clicking a row should open a new 'Contract Detail' tab.
