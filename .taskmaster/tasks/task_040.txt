# Task ID: 40
# Title: 管理员功能：开发用户管理页面基础框架
# Status: pending
# Dependencies: 29
# Priority: medium
# Description: 为管理员角色创建一个用户管理页面，能够以列表形式展示系统中的所有用户及其角色。
# Details:
1. 创建一个新的`UserManagement.vue`页面组件。 2. 在该组件中，调用`GET /api/users`接口获取所有用户数据。 3. 使用表格（Table）组件将用户数据渲染出来，至少包含用户名、角色、创建时间等列。 4. 确保此页面受路由守卫保护，仅管理员可访问。

# Test Strategy:
1. 以管理员身份登录，导航到用户管理页面。 2. 验证用户列表是否正确显示，并且数据与后端一致。 3. 尝试以非管理员身份访问该页面URL，验证是否被拒绝访问。
