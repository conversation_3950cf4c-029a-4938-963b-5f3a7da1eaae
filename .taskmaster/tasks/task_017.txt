# Task ID: 17
# Title: Backend: Admin User Management APIs
# Status: pending
# Dependencies: 2
# Priority: medium
# Description: Implement the full suite of RESTful APIs for administrators to manage users.
# Details:
Create all endpoints under '/api/admin/users': GET for listing/searching, POST for creating (default password '123456'), PUT for updating role/status, DELETE for soft-deleting, and a specific PUT for resetting passwords.

# Test Strategy:
As an admin, use an API client to test creating a new user, listing all users, changing a user's role, banning a user, and resetting a password. Verify DB changes.
