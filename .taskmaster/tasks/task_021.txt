# Task ID: 21
# Title: Frontend: Role-based Menu and Route Guards
# Status: pending
# Dependencies: 5
# Priority: medium
# Description: Finalize the role-based access control on the frontend, ensuring menus and routes are correctly restricted.
# Details:
Use 'vue-router' navigation guards ('beforeEach') to check user role from the auth store before allowing access to a route. Dynamically render the sidebar menu items based on the user's role.

# Test Strategy:
Log in as each role (employee, reviewer, admin). Verify the sidebar menu is correct for each. Attempt to manually navigate to a restricted URL (e.g., employee trying to access '/admin/users') and confirm they are blocked or redirected.
