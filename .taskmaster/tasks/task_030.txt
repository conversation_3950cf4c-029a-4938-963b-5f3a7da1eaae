# Task ID: 30
# Title: 前端：开发合同上传时的审核员选择功能
# Status: pending
# Dependencies: 28
# Priority: medium
# Description: 为“员工”角色在合同上传页面添加一个下拉选择框，允许其选择将合同提交给县局或市局审核员。
# Details:
1. 在合同上传的Vue组件中，添加一个`<select>`或UI库的等效组件。 2. 创建一个新的API端点 `GET /api/reviewers`，用于返回所有县局和市局审核员的列表。 3. 在组件加载时调用此API，并用返回的数据填充下拉框。 4. 将用户选择的审核员ID包含在合同提交的请求载荷中。

# Test Strategy:
1. 以“员工”角色登录并进入合同上传页面。 2. 验证审核员选择框是否存在并且已填充了审核员列表。 3. 选择一个审核员并提交合同，使用浏览器开发者工具检查网络请求，确认请求体中包含了正确的`reviewerId`。
