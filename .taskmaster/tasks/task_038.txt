# Task ID: 38
# Title: 前端：集成图表库并创建审核趋势图
# Status: pending
# Dependencies: None
# Priority: medium
# Description: 在审核统计页面，集成一个可视化图表库（如ECharts），并开发第一个图表：审核趋势图。
# Details:
1. 安装`echarts`和`vue-echarts`。 2. 在统计页面组件中，引入并注册ECharts组件。 3. 创建一个API端点`GET /api/statistics/trends`，返回按时间（日/周/月）聚合的审核数量。 4. 在组件中调用此API，并将返回的数据配置为ECharts的option，渲染一个折线图或柱状图。

# Test Strategy:
1. 访问审核统计页面。 2. 验证“审核趋势图”是否已成功渲染。 3. 检查图表的X轴（时间）和Y轴（数量）是否正确，数据点是否与API返回的模拟数据一致。
