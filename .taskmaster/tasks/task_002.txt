# Task ID: 2
# Title: Backend: Authentication API and Middleware
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Implement user authentication endpoints for login, logout, and profile retrieval. Create middleware to protect routes based on user roles.
# Details:
Endpoints: POST /api/auth/login, POST /api/auth/logout, GET /api/auth/profile. Use a simple session-based mechanism. The middleware should check for a valid session and user role ('employee', 'reviewer', 'admin') for protected routes.

# Test Strategy:
Use an API client like Postman to test login with valid/invalid credentials, access a protected route with/without a session, and check role-based access.
