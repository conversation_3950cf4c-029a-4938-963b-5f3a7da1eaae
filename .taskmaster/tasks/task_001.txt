# Task ID: 1
# Title: Backend: Project Scaffolding and Database Setup
# Status: pending
# Dependencies: None
# Priority: high
# Description: Initialize the Node.js/Express backend project, create the folder structure (routes, models, middleware), and set up the SQLite database with 'users' and 'contracts' tables.
# Details:
Use Express.js for the server. Define the schema for 'users' and 'contracts' as specified in the PRD. Create an initialization script (e.g., 'npm run init-db') to create the tables and insert the default 'admin' user with a hashed password.

# Test Strategy:
Run the init script and verify the 'database.sqlite' file is created with the correct tables and the admin user exists.
