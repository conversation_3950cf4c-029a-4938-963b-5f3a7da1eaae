# Task ID: 5
# Title: Frontend: Main Layout with Sidebar and Tab Management
# Status: pending
# Dependencies: 4
# Priority: high
# Description: Implement the main application layout featuring a left sidebar for navigation and a dynamic tabbed interface for content, as described in the PRD.
# Details:
Create a 'Layout.vue' component using Element Plus's Container components. The sidebar menu ('el-menu') should be role-based. Implement a 'useTabs' composable to manage opening, closing, and switching between tabs.

# Test Strategy:
Log in as different roles and verify the sidebar menu items change accordingly. Click menu items to open new tabs and ensure they can be closed (except for the 'Home' tab).
