# Task ID: 8
# Title: Frontend: Contract Submission Tab Page
# Status: pending
# Dependencies: 5, 7
# Priority: high
# Description: Create the 'Submit Contract' tab page for employees to upload a PDF and submit it for review.
# Details:
Create 'src/components/tabs/SubmitPage.vue'. Use Element Plus 'el-upload' for file upload (calling the file upload API first) and an 'el-form' for selecting a reviewer from a pre-set list. On submit, call the contract submission API.

# Test Strategy:
As an employee, open the 'Submit Contract' tab, upload a PDF, select a reviewer, and submit. Verify a success message with the new serial number is shown.
