# Task ID: 19
# Title: Backend: Admin System Monitoring APIs
# Status: pending
# Dependencies: 9, 17
# Priority: low
# Description: Create APIs for the admin to view all contracts and get simple system statistics.
# Details:
Endpoints: 'GET /api/admin/contracts' to list all contracts in the system with pagination and filtering. 'GET /api/admin/statistics' to return simple counts like total users, total contracts, etc.

# Test Strategy:
As an admin, call the endpoints and verify they return all data, not just data related to the admin user. Check that the statistics are accurate.
