# Task ID: 12
# Title: Frontend: Reusable PDF Viewer Component
# Status: pending
# Dependencies: 6
# Priority: medium
# Description: Create a dedicated component to embed and display PDF files fetched from the backend.
# Details:
Create 'src/components/common/PdfViewer.vue' using the 'vue-pdf-embed' library. The component should accept a file URL as a prop and render the PDF.

# Test Strategy:
Pass a valid PDF URL from the backend to the component and verify it renders correctly within a page.
