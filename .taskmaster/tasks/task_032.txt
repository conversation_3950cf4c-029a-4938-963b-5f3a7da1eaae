# Task ID: 32
# Title: 前端：集成PDF.js实现PDF预览缩放功能
# Status: pending
# Dependencies: None
# Priority: high
# Description: 集成`PDF.js`或其Vue封装库，为合同预览功能增加“放大”、“缩小”和“适应页面”的控制按钮。
# Details:
1. 安装`pdfjs-dist`或`vue-pdf-embed`等库。 2. 创建一个`PdfViewer.vue`组件，用于渲染PDF文档。 3. 在组件中添加三个按钮：放大、缩小、适应页面。 4. 将按钮的点击事件分别绑定到PDF.js库提供的缩放控制API上。

# Test Strategy:
1. 打开一个合同的预览页面。 2. 点击“放大”和“缩小”按钮，验证PDF视图是否相应地缩放。 3. 点击“适应页面”按钮，验证PDF页面是否完整地显示在可视区域内。
