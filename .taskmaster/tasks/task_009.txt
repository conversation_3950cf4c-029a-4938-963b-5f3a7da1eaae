# Task ID: 9
# Title: Backend: Contract List APIs for All Roles
# Status: pending
# Dependencies: 7
# Priority: medium
# Description: Implement various GET endpoints to fetch lists of contracts tailored to different user roles, with pagination.
# Details:
Endpoints: 'GET /api/contracts/my' for employees, 'GET /api/contracts/pending' and 'GET /api/contracts/reviewed' for reviewers. All endpoints must support 'page' and 'size' query parameters for pagination.

# Test Strategy:
Call each endpoint as the appropriate user role. Test pagination parameters ('page', 'size') and verify the response structure contains 'contracts', 'total', 'page', and 'size'.
