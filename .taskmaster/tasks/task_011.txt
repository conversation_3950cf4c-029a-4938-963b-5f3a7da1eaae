# Task ID: 11
# Title: Backend: Contract Detail and Review Action APIs
# Status: pending
# Dependencies: 7
# Priority: high
# Description: Create APIs to fetch a single contract's details and to perform review actions (start review, approve, reject).
# Details:
Endpoints: 'GET /api/contracts/:id' to get full details. 'PUT /api/contracts/:id/start-review' to change status to 'reviewing'. 'PUT /api/contracts/:id/review' to set status to 'approved' or 'rejected' and save the review comment.

# Test Strategy:
Fetch a contract by ID. Call the 'start-review' endpoint and verify status change. Call the 'review' endpoint with 'approved'/'rejected' and check that status and comment are updated in the database.
