# Task ID: 24
# Title: Documentation: Create a Basic README
# Status: pending
# Dependencies: 1
# Priority: low
# Description: Write a README.md file for the project root, detailing the project setup, installation, and how to run the application.
# Details:
Include sections for: Project Overview, Tech Stack, Environment Requirements (Node.js version), Installation Steps (backend and frontend), and Default User Credentials.

# Test Strategy:
Ask a new person to follow the README to set up and run the project. They should be able to do so without further instruction.
