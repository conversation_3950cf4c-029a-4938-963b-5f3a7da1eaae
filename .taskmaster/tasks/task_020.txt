# Task ID: 20
# Title: Frontend: Admin Dashboard and Contract List Pages
# Status: pending
# Dependencies: 18, 19
# Priority: low
# Description: Create the UI tabs for the administrator to monitor the system.
# Details:
Create a 'HomePage.vue' for the admin role that displays the simple stats from the statistics API. Create a separate admin-only tab to display the list of all contracts, reusing the contract table component.

# Test Strategy:
Log in as admin. Verify the dashboard shows correct numbers. Go to the all contracts list and verify it shows contracts from all users.
