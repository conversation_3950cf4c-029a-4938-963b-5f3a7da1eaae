# Task ID: 16
# Title: Frontend: Contract Modification and Re-submission Flow
# Status: pending
# Dependencies: 10, 15
# Priority: medium
# Description: Enable the UI for employees to modify and re-submit a rejected contract.
# Details:
In the 'My Contracts' table ('ContractsPage.vue'), show a 'Modify' button for contracts with 'rejected' or 'pending' status. Clicking it should open a view similar to the submission page, pre-filled with existing data, allowing the user to upload a new PDF and re-submit.

# Test Strategy:
As an employee, find a rejected contract in 'My Contracts'. Click 'Modify', upload a new file, and re-submit. Verify the contract status changes to 'pending' and it appears in the reviewer's queue again.
