# Task ID: 14
# Title: Frontend: Contract Detail and Review Action UI
# Status: pending
# Dependencies: 11, 12, 13
# Priority: high
# Description: Build the contract detail tab, which shows contract information, the PDF preview, and the review action form.
# Details:
Create 'src/components/tabs/ContractDetailPage.vue'. This dynamic tab should fetch data from '/api/contracts/:id', display metadata, embed the 'PdfViewer.vue' component, and show a form for reviewers to approve/reject with comments. The form should only be visible to the assigned reviewer.

# Test Strategy:
As a reviewer, open a contract detail tab. Verify all information and the PDF are displayed. Submit an approval/rejection and confirm the UI updates and the tab can be closed.
