# Task ID: 15
# Title: Backend: Contract Modification API
# Status: pending
# Dependencies: 11
# Priority: medium
# Description: Implement the API to allow an employee to modify a contract that is in a 'pending' or 'rejected' state.
# Details:
Create 'PUT /api/contracts/:id'. This endpoint should validate that the user is the original submitter and the contract status is 'pending' or 'rejected'. It allows updating the associated file and notes, but keeps the same serial number. The status should be reset to 'pending' if it was 'rejected'.

# Test Strategy:
Attempt to update a contract in 'reviewing' state (should fail). Attempt to update a 'rejected' contract with a new file (should succeed). Verify the file path is updated and status is 'pending' in the DB.
