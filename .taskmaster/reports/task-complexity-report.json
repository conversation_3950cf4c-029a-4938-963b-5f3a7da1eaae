{"meta": {"generatedAt": "2025-07-22T04:44:04.362Z", "tasksAnalyzed": 40, "totalTasks": 40, "analysisCount": 40, "thresholdScore": 5, "projectName": "Taskmaster", "usedResearch": true}, "complexityAnalysis": [{"taskId": 1, "taskTitle": "Backend: Project Scaffolding and Database Setup", "complexityScore": 3, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task 'Backend: Project Scaffolding and Database Setup' into 3 subtasks: 1. Initialize the Node.js/Express project using `npm init` and install core dependencies (Express, sqlite3). Create the standard folder structure (e.g., `src/controllers`, `src/routes`, `src/models`, `src/middleware`). 2. Define the SQL schema for the 'users' and 'contracts' tables in a `database/schema.sql` file, specifying all columns and constraints. 3. Create a `database/init.js` script that can be run via `npm run init-db`. This script should delete any existing database file, create a new one, execute the `schema.sql`, and insert the default 'admin' user with a bcrypt-hashed password.", "reasoning": "Low complexity. This is a standard, foundational task that involves well-documented procedures for setting up a Node.js project and a simple database schema. It requires no complex business logic."}, {"taskId": 2, "taskTitle": "Backend: Authentication API and Middleware", "complexityScore": 5, "recommendedSubtasks": 4, "expansionPrompt": "Expand the task 'Backend: Authentication API and Middleware' into 4 subtasks: 1. Implement the `POST /api/auth/login` endpoint, including request body validation, user lookup, password comparison using bcrypt, and creating a session upon successful login. 2. Implement the `POST /api/auth/logout` endpoint to destroy the session and the `GET /api/auth/profile` endpoint to return the current user's data from the session. 3. Create a generic `isAuthenticated` middleware that checks for the existence of a valid session and attaches the user object to the request. 4. Create a `hasRole` middleware factory that takes a role (or array of roles) and returns a middleware function that checks if the authenticated user has the required role, to be used for protecting specific routes.", "reasoning": "Medium-low complexity. While authentication is a common feature, it involves multiple moving parts: secure password handling, session management, and creating reusable middleware for access control, which requires careful implementation to be secure."}, {"taskId": 3, "taskTitle": "Frontend: Project Setup and UI Framework Integration", "complexityScore": 2, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task 'Frontend: Project Setup and UI Framework Integration' into 3 subtasks: 1. Initialize a new Vue 3 project using the `npm create vite@latest` command and clean up the default boilerplate. 2. Install and configure the Element Plus UI library according to its official documentation, ensuring components are auto-imported or globally registered. 3. Install and configure Tailwind CSS, creating the `tailwind.config.js` and `postcss.config.js` files and importing Tailwind's base styles into the main CSS file.", "reasoning": "Very low complexity. This task consists of running standard CLI commands and following well-defined setup guides for popular frontend tools. It's a configuration-heavy but logically simple task."}, {"taskId": 4, "taskTitle": "Frontend: API Client and Login Page", "complexityScore": 4, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task 'Frontend: API Client and Login Page' into 3 subtasks: 1. Create an Axios instance in `src/api/index.js` with a configured `baseURL`. Implement a response interceptor to handle global API errors. 2. Build the UI for the `Login.vue` page using Element Plus form components (`el-form`, `el-form-item`, `el-input`, `el-button`). 3. Implement the login logic: handle form submission, call the login API via the Axios instance, store the returned user profile and session state in a Pinia store, and redirect to the main layout on success.", "reasoning": "Low complexity. Building a login form is a standard task. The use of Axios interceptors adds a slight layer of complexity over a basic fetch call, but it's a common and well-documented pattern."}, {"taskId": 5, "taskTitle": "Frontend: Main Layout with Sidebar and Tab Management", "complexityScore": 6, "recommendedSubtasks": 4, "expansionPrompt": "Expand the task 'Frontend: Main Layout with Sidebar and Tab Management' into 4 subtasks: 1. Create the main `Layout.vue` component using Element Plus's `el-container`, `el-aside`, `el-header`, and `el-main` to structure the page. 2. Implement a dynamic sidebar menu (`el-menu`) whose items are generated from a route configuration array, filtered based on the current user's role. 3. Develop the tab management UI, which displays a list of open tabs and allows closing them. Clicking a tab should switch the content in the `<router-view>`. 4. Create a `useTabs` Vue composable to handle the logic of adding, removing, and switching between tabs, storing the list of open tabs in a reactive state.", "reasoning": "Medium complexity. While the layout structure is simple, implementing a dynamic, multi-tab interface from scratch involves significant state management logic that can be tricky to get right, especially regarding reactivity and persistence."}, {"taskId": 6, "taskTitle": "Backend: File Upload and Serving Endpoints", "complexityScore": 4, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task 'Backend: File Upload and Serving Endpoints' into 3 subtasks: 1. Install and configure the `multer` middleware, setting the storage destination to `./uploads` and implementing a file filter to accept only PDFs and a size limit of 10MB. 2. Create the `POST /api/files/upload` endpoint that uses the configured `multer` middleware to process the `multipart/form-data` request and returns a success response with the generated filename. 3. Create the `GET /api/files/:id/preview` endpoint that safely resolves the file path from the `:id` parameter, sets the `Content-Type` header to `application/pdf`, and serves the static file for in-browser viewing.", "reasoning": "Low complexity. The `multer` library abstracts away most of the complexity of handling file uploads. The task is primarily about configuration and wiring up the middleware to Express routes."}, {"taskId": 7, "taskTitle": "Backend: Contract Submission API", "complexityScore": 5, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task 'Backend: Contract Submission API' into 3 subtasks: 1. Define the `POST /api/contracts` route and controller. Implement input validation to ensure all required fields (file info, reviewer ID, notes) are present. 2. Implement the logic for generating a unique, sequential serial number. This should involve querying the database for the highest existing number, incrementing it, and formatting it (e.g., 'HT' + padded number). 3. Implement the database insertion logic to create a new record in the `contracts` table with the validated data, the new serial number, the submitter's ID from the session, and a default status of 'pending'.", "reasoning": "Medium-low complexity. The core of the task is a standard database insert. The main complexity lies in correctly and safely implementing the sequential serial number generation, which requires a read-then-write operation."}, {"taskId": 8, "taskTitle": "Frontend: Contract Submission Tab Page", "complexityScore": 5, "recommendedSubtasks": 4, "expansionPrompt": "Expand the task 'Frontend: Contract Submission Tab Page' into 4 subtasks: 1. Create the `SubmitPage.vue` component and its corresponding route, ensuring it can be opened in the tabbed layout. 2. Build the UI using `el-form` and `el-upload`. The form should include fields for notes and a dropdown to select a reviewer. 3. Configure the `el-upload` component to automatically upload the file to the `POST /api/files/upload` endpoint upon selection and store the returned file information in the component's state. 4. Implement the main form's submit handler. On submit, it should gather all form data, including the ID of the uploaded file, and send it to the `POST /api/contracts` API, then show a success message.", "reasoning": "Medium-low complexity. This task requires coordinating two asynchronous API calls (file upload, then form submission). Managing the state between these two steps adds a layer of complexity to an otherwise standard form implementation."}, {"taskId": 9, "taskTitle": "Backend: Contract List APIs for All Roles", "complexityScore": 6, "recommendedSubtasks": 4, "expansionPrompt": "Expand the task 'Backend: Contract List APIs for All Roles' into 4 subtasks: 1. Create a reusable utility function or middleware to parse `page` and `size` query parameters and calculate the `LIMIT` and `OFFSET` for SQL queries. 2. Implement the `GET /api/contracts/my` endpoint, which filters the `contracts` table by the `submitter_id` matching the logged-in user's ID. 3. Implement the `GET /api/contracts/pending` and `GET /api/contracts/reviewed` endpoints, which filter contracts by the `reviewer_id` matching the logged-in user's ID and the appropriate status. 4. For all endpoints, structure the response to include the paginated list of contracts as well as metadata: `total` (total number of records), `page`, and `size`.", "reasoning": "Medium complexity. Implementing correct and secure data filtering for different roles, combined with robust pagination logic (including a total count query), makes this more complex than a simple data fetch. It requires careful SQL query construction."}, {"taskId": 10, "taskTitle": "Frontend: 'My Contracts' <PERSON><PERSON> Page for Employees", "complexityScore": 4, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task 'Frontend: 'My Contracts' Tab Page for Employees' into 3 subtasks: 1. Create the `ContractsPage.vue` component and add it to the router and role-based menu for employees. 2. Implement the data fetching logic to call the `/api/contracts/my` endpoint, including handling of pagination parameters (`page`, `size`) and managing loading/error states. 3. Use an `el-table` to display the list of contracts. Configure the table columns and bind its pagination component events (`@current-change`, `@size-change`) to re-fetch the data with new parameters.", "reasoning": "Low complexity. This is a very common 'display data in a paginated table' task. Modern UI libraries like Element Plus provide powerful table components that handle much of the UI state, simplifying the implementation."}, {"taskId": 11, "taskTitle": "Backend: Contract Detail and Review Action APIs", "complexityScore": 6, "recommendedSubtasks": 4, "expansionPrompt": "Expand the task 'Backend: Contract Detail and Review Action APIs' into 4 subtasks: 1. Implement the `GET /api/contracts/:id` endpoint to fetch a single contract by its ID, joining with the users table to include submitter/reviewer names. 2. Implement the `PUT /api/contracts/:id/start-review` endpoint. It must verify the user is the assigned reviewer and the status is 'pending' before updating the status to 'reviewing'. 3. Implement the `PUT /api/contracts/:id/review` endpoint. It must validate the incoming status ('approved' or 'rejected'), verify the user is the reviewer, and update the contract's status and review comment. 4. Add a new `contract_history` table and logic to log every status change (submission, review start, approval/rejection) with a timestamp and user ID for auditing purposes.", "reasoning": "Medium complexity. This task involves multiple endpoints with state-transition logic and business rules. The authorization (is this user allowed to perform this action on this specific contract?) is more complex than a simple role check."}, {"taskId": 12, "taskTitle": "Frontend: Reusable PDF Viewer Component", "complexityScore": 3, "recommendedSubtasks": 2, "expansionPrompt": "Expand the task 'Frontend: Reusable PDF Viewer Component' into 2 subtasks: 1. Install the `vue-pdf-embed` library. 2. Create a new component `src/components/common/PdfViewer.vue` that accepts a `source` URL as a prop and uses the `vue-pdf-embed` component internally to render the PDF, while also handling loading and error states.", "reasoning": "Low complexity. This task is primarily about wrapping a third-party library in a custom component. The complexity is minimal as long as the library is well-documented and works as expected."}, {"taskId": 13, "taskTitle": "Frontend: Reviewer's 'Pending Review' Tab Page", "complexityScore": 4, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task 'Frontend: Reviewer's 'Pending Review' Tab Page' into 3 subtasks: 1. Create the `ReviewPage.vue` component and add it to the router and role-based menu for reviewers. 2. Implement the data fetching logic to call the `/api/contracts/pending` endpoint with pagination. 3. Use an `el-table` to display the list of pending contracts and implement a `@row-click` event handler that uses the tab management system to open a new `ContractDetailPage` for the clicked contract's ID.", "reasoning": "Low complexity. This is very similar to Task 10, reusing the same pattern of a paginated table. The only new piece is the row-click navigation, which is a minor addition."}, {"taskId": 14, "taskTitle": "Frontend: Contract Detail and Review Action UI", "complexityScore": 7, "recommendedSubtasks": 5, "expansionPrompt": "Expand the task 'Frontend: Contract Detail and Review Action UI' into 5 subtasks: 1. Create the `ContractDetailPage.vue` component, configured to accept a contract ID from the route parameters. 2. On component mount, fetch the full contract details from `GET /api/contracts/:id` and store them in the component's state. 3. Build the layout to display the contract's metadata (status, submitter, notes, etc.) in a description list or card. 4. Embed the `PdfViewer.vue` component, passing the correct file preview URL to it. 5. Create the review action form (approve/reject options, comment field, submit button). This form should be conditionally rendered only if the current user is the assigned reviewer and the contract status is 'reviewing'. Implement the logic to call the appropriate review action API on submit.", "reasoning": "Medium-high complexity. This is a composite view that integrates data display, a reusable component (PDF viewer), and a conditional action form. Managing the various states (loading, data display, conditional UI, form submission) makes it a complex piece of the UI."}, {"taskId": 15, "taskTitle": "Backend: Contract Modification API", "complexityScore": 6, "recommendedSubtasks": 4, "expansionPrompt": "Expand the task 'Backend: Contract Modification API' into 4 subtasks: 1. Create the `PUT /api/contracts/:id` route and controller. 2. Implement an authorization check that fetches the contract and verifies that the logged-in user's ID matches the contract's `submitter_id`. 3. Implement a state validation check to ensure the contract's current status is either 'pending' or 'rejected'. If not, return a 403 Forbidden error. 4. Implement the database update logic. If the status was 'rejected', it should be reset to 'pending'. The endpoint should allow updating fields like `file_id` and `notes`.", "reasoning": "Medium complexity. The business logic is nuanced, requiring a combination of ownership check (are you the submitter?) and state check (is the contract in a modifiable state?). This multi-part validation adds complexity."}, {"taskId": 16, "taskTitle": "Frontend: Contract Modification and Re-submission Flow", "complexityScore": 6, "recommendedSubtasks": 4, "expansionPrompt": "Expand the task 'Frontend: Contract Modification and Re-submission Flow' into 4 subtasks: 1. In the 'My Contracts' table (`ContractsPage.vue`), add a 'Modify' button to each row, conditionally rendered only for contracts with 'rejected' or 'pending' status. 2. Make the 'Modify' button navigate to a new route like `/contracts/:id/edit`. This route should reuse the `SubmitPage.vue` component. 3. In `SubmitPage.vue`, add logic to detect if an `id` is present in the route params. If so, fetch the existing contract data and pre-fill the form fields. 4. Modify the form's submit handler to call the `PUT /api/contracts/:id` API when in edit mode, instead of the POST API for creation.", "reasoning": "Medium complexity. Reusing a form component for both 'create' and 'edit' modes is an efficient but non-trivial pattern. It requires careful handling of component lifecycle, data fetching, and conditional logic within the component."}, {"taskId": 17, "taskTitle": "Backend: Admin User Management APIs", "complexityScore": 7, "recommendedSubtasks": 5, "expansionPrompt": "Expand the task 'Backend: Admin User Management APIs' into 5 subtasks: 1. Implement `GET /api/admin/users` for listing all users with pagination and search capabilities (e.g., by username). 2. Implement `POST /api/admin/users` to create a new user, ensuring the password is properly hashed before saving. 3. Implement `PUT /api/admin/users/:id` to update user information such as their role or active status. 4. Implement `DELETE /api/admin/users/:id` to perform a soft delete by setting an `is_deleted` flag or `deleted_at` timestamp. 5. Implement a separate `PUT /api/admin/users/:id/reset-password` endpoint to reset a user's password to a system default.", "reasoning": "Medium-high complexity. This task requires implementing a full CRUDL (Create, Read, Update, Delete, List) suite of endpoints for a resource. Each endpoint has its own specific logic (hashing, soft-deleting, etc.), and the sheer number of operations makes it a substantial task."}, {"taskId": 18, "taskTitle": "Frontend: Admin User Management Page", "complexityScore": 7, "recommendedSubtasks": 5, "expansionPrompt": "Expand the task 'Frontend: Admin User Management Page' into 5 subtasks: 1. Create the `UserManagePage.vue` component with an `el-table` to list users fetched from the API, including a 'Create User' button. 2. Implement the 'Create User' flow using an `el-dialog` that contains a form for the new user's details. 3. Implement the 'Edit User' flow, where an 'Edit' button in the table opens a pre-filled dialog to update the user's role or status. 4. Implement the 'Delete' and 'Reset Password' actions for each user in the table, using `ElMessageBox.confirm` to get user confirmation before calling the respective APIs. 5. Add search and pagination controls to the page that are wired to the user list API.", "reasoning": "Medium-high complexity. Building a full CRUD UI is a significant undertaking. It involves multiple components (table, dialogs, forms), extensive state management, and connecting numerous UI actions to their corresponding API calls."}, {"taskId": 19, "taskTitle": "Backend: Admin System Monitoring APIs", "complexityScore": 4, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task 'Backend: Admin System Monitoring APIs' into 3 subtasks: 1. Implement the `GET /api/admin/contracts` endpoint. This should be similar to other contract list APIs but without user-specific filtering, allowing an admin to see all contracts. It must support pagination and filtering. 2. Implement the `GET /api/admin/statistics` endpoint. This should execute several simple database queries (e.g., `SELECT COUNT(*) FROM users`, `SELECT COUNT(*) FROM contracts WHERE status = 'pending'`) and return the results in a single JSON object. 3. Apply an admin-only authorization middleware to both of these routes to ensure they are secure.", "reasoning": "Low complexity. These are read-only endpoints that involve straightforward database queries. The logic is simpler than role-specific lists as the primary requirement is to fetch all data, not a filtered subset."}, {"taskId": 20, "taskTitle": "Frontend: Admin Dashboard and Contract List Pages", "complexityScore": 5, "recommendedSubtasks": 4, "expansionPrompt": "Expand the task 'Frontend: Admin Dashboard and Contract List Pages' into 4 subtasks: 1. Create a new `AdminDashboard.vue` component to serve as the admin's homepage. 2. In the dashboard component, call the `/api/admin/statistics` API and display the returned numbers in a series of `el-card` components. 3. Create a new `AdminContractsListPage.vue` component. 4. In the contracts list component, fetch data from the `/api/admin/contracts` endpoint and display it using a reusable contract table component, ensuring pagination and filtering work correctly.", "reasoning": "Medium-low complexity. The task involves creating new views and wiring them to new APIs. The complexity is reduced by the potential to reuse existing components (like a contract table) and the simple nature of the dashboard display."}, {"taskId": 21, "taskTitle": "Frontend: Role-based Menu and Route Guards", "complexityScore": 5, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task 'Frontend: Role-based Menu and Route Guards' into 3 subtasks: 1. In `src/router/index.js`, implement a `router.beforeEach` global navigation guard. This guard should check for a `meta.role` property on the target route and compare it with the user's role from the Pinia auth store. 2. If the user is not authenticated, redirect to '/login'. If they are authenticated but do not have the required role, redirect them to a '403 Forbidden' page. 3. Refactor the sidebar menu component to dynamically generate its links by filtering a master route list based on the current user's role, ensuring users only see links they are allowed to access.", "reasoning": "Medium-low complexity. The logic for a navigation guard is not overly complex, but it's a critical, cross-cutting concern that affects the entire application's security and user experience, so it must be implemented carefully."}, {"taskId": 22, "taskTitle": "Composables: Create Reusable Logic for Data Fetching", "complexityScore": 6, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task 'Composables: Create Reusable Logic for Data Fetching' into 3 subtasks: 1. Create a `src/composables/useTableData.js` composable that accepts a function to fetch data and manages generic table state: a reactive list of items, loading state, error state, and pagination data. 2. Create a `src/composables/useContractsApi.js` composable that encapsulates the specific Axios calls for all contract-related operations (getList, getById, create, update, etc.). 3. Refactor the existing contract list components (`ContractsPage.vue`, `ReviewPage.vue`) to use these new composables, simplifying the component's script section significantly.", "reasoning": "Medium complexity. Refactoring existing code into well-designed, reusable abstractions is often more challenging than the initial implementation. Designing a good generic composable like `useTableData` requires careful thought about its API."}, {"taskId": 23, "taskTitle": "System-wide: <PERSON><PERSON><PERSON> and User Feedback", "complexityScore": 5, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task 'System-wide: Error Handling and User Feedback' into 3 subtasks: 1. In the Axios instance, add a response interceptor to globally catch network errors and API error responses (e.g., status codes 4xx, 5xx). 2. In the interceptor, use a global notification service like Element Plus's `ElMessage` to display a user-friendly, non-technical error message for unhandled errors (e.g., 'An unexpected error occurred. Please try again.'). 3. For specific user actions like login or form submission, add `.catch()` blocks to the API calls to provide more contextual feedback (e.g., 'Invalid username or password').", "reasoning": "Medium-low complexity. The technical implementation in an Axios interceptor is straightforward. The main challenge is ensuring a consistent and user-friendly approach across the entire application, which is more of a design and consistency task."}, {"taskId": 24, "taskTitle": "Documentation: Create a Basic README", "complexityScore": 2, "recommendedSubtasks": 2, "expansionPrompt": "Expand the task 'Documentation: Create a Basic README' into 2 subtasks: 1. Write the content for the `README.md` file, including sections for Project Overview, Technology Stack (Node.js, Vue 3, etc.), Setup and Installation (for both backend and frontend), and How to Run (including the `init-db` script and default admin credentials). 2. Ask a team member who is less familiar with the project to follow the README instructions on a clean machine to set up and run the application, and update the document based on their feedback.", "reasoning": "Very low complexity. This is a non-coding task that involves documenting existing processes. It is straightforward and requires only organizational skills and clarity in writing."}, {"taskId": 25, "taskTitle": "Final E2E Testing and Bug Fixing", "complexityScore": 8, "recommendedSubtasks": 5, "expansionPrompt": "Expand the task 'Final E2E Testing and Bug Fixing' into 5 subtasks representing the core user journey: 1. Test the Admin setup flow: Log in as admin, create a new 'employee' user and a new 'reviewer' user. 2. Test the Employee submission flow: Log out, log in as the new employee, and successfully submit a new contract for review by the new reviewer. 3. Test the Reviewer rejection flow: Log out, log in as the reviewer, find the pending contract, and reject it with a comment. 4. Test the Employee resubmission flow: Log in as the employee, find the rejected contract, use the 'Modify' feature to upload a new file, and re-submit it. 5. Test the Reviewer approval flow: Log in as the reviewer, find the re-submitted contract, and approve it. Verify the final status is correct for all parties.", "reasoning": "High complexity. End-to-end testing is inherently complex as it validates the integration of all system components (frontend, backend, database). This task is open-ended, as the 'bug fixing' portion can uncover issues of varying difficulty."}, {"taskId": 26, "taskTitle": "登录页面品牌化：更新标题与Logo", "complexityScore": 1, "recommendedSubtasks": 2, "expansionPrompt": "Expand the task '登录页面品牌化：更新标题与Logo' into 2 subtasks: 1. Modify the `<title>` tag in the main `public/index.html` file to '宿迁烟草合同审核系统'. 2. Place the new `logo.png` in the `src/assets` directory and update the `<img>` tag's `src` attribute in the `Login.vue` component to point to the new logo.", "reasoning": "Trivial complexity. This is a cosmetic task involving changing a text string and an image path, with no logic or complex implementation required."}, {"taskId": 27, "taskTitle": "登录页面品牌化：优化视觉设计", "complexityScore": 3, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task '登录页面品牌化：优化视觉设计' into 3 subtasks: 1. Define the brand's primary and secondary colors as CSS custom properties in a global stylesheet (e.g., `src/styles/variables.css`). 2. In the `Login.vue` component's stylesheet, apply the new brand colors to key elements like the login button's background color and link text colors. 3. Adjust the layout and spacing of the login form, logo, and background using CSS to ensure a professional and visually balanced appearance.", "reasoning": "Low complexity. This is a standard CSS styling task. It requires design sense but is not technically complex, involving the application of colors and adjustment of layout properties."}, {"taskId": 28, "taskTitle": "后端：定义四级用户角色体系", "complexityScore": 4, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task '后端：定义四级用户角色体系' into 3 subtasks: 1. Modify the `users` table schema to include a `role` column (e.g., `TEXT` or `INTEGER` with a foreign key to a new `roles` table). 2. If a new `roles` table is created, populate it with the four roles: '员工', '县局审核员', '市局审核员', '管理员'. 3. Update the backend logic for user creation and user profile retrieval (`GET /api/auth/profile`) to correctly handle and return the new role information.", "reasoning": "Low complexity. This is primarily a database schema and data modeling task. The required API changes are minor, focusing on including the new role field in data structures."}, {"taskId": 29, "taskTitle": "前端：实现基于角色的路由保护", "complexityScore": 5, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task '前端：实现基于角色的路由保护' into 3 subtasks: 1. In the Vue Router configuration (`src/router/index.js`), add a `meta: { requiredRole: 'admin' }` property to routes that need protection. 2. Implement a global `router.beforeEach` navigation guard that checks if a route has `requiredRole` metadata. 3. Inside the guard, compare the route's `requiredRole` with the current user's role (from a Pinia store). If they don't have access, redirect them to a '403 Forbidden' page or the home page.", "reasoning": "Medium-low complexity. This is a duplicate of Task 21. The logic is straightforward, but its implementation in a global navigation guard is critical for application security and requires careful handling."}, {"taskId": 30, "taskTitle": "前端：开发合同上传时的审核员选择功能", "complexityScore": 5, "recommendedSubtasks": 4, "expansionPrompt": "Expand the task '前端：开发合同上传时的审核员选择功能' into 4 subtasks: 1. Backend: Create a new API endpoint `GET /api/users/reviewers` that returns a list of all users with the '县局审核员' or '市局审核员' role. 2. Frontend: In the contract submission component (`SubmitPage.vue`), call this new API when the component is created. 3. Frontend: Add an `el-select` (dropdown) component to the form and populate it with the list of reviewers fetched from the API. 4. Frontend: Ensure the selected reviewer's ID is captured and included in the payload when the contract submission form is submitted.", "reasoning": "Medium-low complexity. This task requires coordinated work on both the backend (new endpoint) and frontend (UI change and API call). While each part is simple, it involves the full stack."}, {"taskId": 31, "taskTitle": "前端：开发右上角通知中心UI框架", "complexityScore": 4, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task '前端：开发右上角通知中心UI框架' into 3 subtasks: 1. In the main application header, add a bell icon button, possibly using Element Plus's `el-badge` to show a notification count. 2. Create a `NotificationPanel.vue` component that will be displayed in an `el-popover` or `el-dropdown` when the bell icon is clicked. 3. Populate the `NotificationPanel` with a static list of mock notifications to establish the UI structure, including elements for title, time, and read/unread status.", "reasoning": "Low complexity. This is a UI-focused task to build the component shell and its basic open/close behavior. It uses mock data, so no backend integration is needed at this stage."}, {"taskId": 32, "taskTitle": "前端：集成PDF.js实现PDF预览缩放功能", "complexityScore": 4, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task '前端：集成PDF.js实现PDF预览缩放功能' into 3 subtasks: 1. Ensure a PDF viewing library (like `vue-pdf-embed` which uses PDF.js) is installed and integrated into a `PdfViewer.vue` component. 2. Add a UI toolbar to the `PdfViewer` component with 'Zoom In', 'Zoom Out', and 'Fit to Page' buttons. 3. Connect the click events of these buttons to the corresponding methods or properties of the PDF viewing library to control the zoom level.", "reasoning": "Low complexity. This task involves using the documented API of an existing library. The work is primarily about adding UI controls and calling the correct functions."}, {"taskId": 33, "taskTitle": "前端：为PDF预览增加全屏和翻页功能", "complexityScore": 5, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task '前端：为PDF预览增加全屏和翻页功能' into 3 subtasks: 1. Add 'Previous' and 'Next' buttons to the PDF viewer toolbar, along with a 'Page X of Y' display. Wire the buttons to the library's page navigation functions. 2. Add an input field to allow users to type a page number and jump directly to it. 3. Add a 'Fullscreen' button that uses the browser's Fullscreen API to toggle the viewer element into and out of fullscreen mode.", "reasoning": "Medium-low complexity. Managing page state adds a bit more complexity than simple zooming. The Fullscreen API is standard but can have minor browser-specific quirks to handle."}, {"taskId": 34, "taskTitle": "前端：实现合同打印功能", "complexityScore": 3, "recommendedSubtasks": 2, "expansionPrompt": "Expand the task '前端：实现合同打印功能' into 2 subtasks: 1. Add a 'Print' button to the PDF viewer's UI that, when clicked, calls the `window.print()` JavaScript method. 2. Create a dedicated print stylesheet using the `@media print` query. In this stylesheet, hide all non-essential UI elements (header, sidebar, toolbars, etc.) so that only the PDF content is printed.", "reasoning": "Low complexity. The core JavaScript is trivial. The main effort is in writing CSS to create a clean print layout, which is a well-understood technique."}, {"taskId": 35, "taskTitle": "前端：开发用户头像上传UI", "complexityScore": 3, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task '前端：开发用户头像上传UI' into 3 subtasks: 1. On the user profile page, add a visually hidden `<input type=\"file\" accept=\"image/*\">`. 2. Add a visible 'Upload Avatar' button or icon that programmatically triggers a click on the hidden file input. 3. Listen for the `change` event on the file input. When a file is selected, use the `FileReader` API to read it as a DataURL and display the image in a preview area.", "reasoning": "Low complexity. This is a classic frontend pattern that uses standard browser APIs and does not yet involve backend communication or complex state."}, {"taskId": 36, "taskTitle": "前端：集成头像裁剪功能", "complexityScore": 5, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task '前端：集成头像裁剪功能' into 3 subtasks: 1. Install and import a Vue image cropping library like `vue-cropperjs`. 2. When a user selects an image to upload, open a modal dialog containing the cropper component and load the selected image into it. 3. Provide a 'Save' button in the modal. On click, use the library's API to get the cropped image data (as a Blob or Base64 string) and store it in the component's state for the final upload.", "reasoning": "Medium-low complexity. This is an integration task. The complexity depends on the chosen library's API and documentation, and it involves managing the state flow from file selection to cropping to final data preparation."}, {"taskId": 37, "taskTitle": "前端：优化首页快捷操作按钮", "complexityScore": 2, "recommendedSubtasks": 2, "expansionPrompt": "Expand the task '前端：优化首页快捷操作按钮' into 2 subtasks: 1. Review the homepage component's template to ensure each shortcut button is either a `<router-link>` with a correct `:to` prop or has a `@click` handler that correctly calls `router.push()`. 2. Manually test each button by clicking it and verifying that it navigates to the correct page without any console errors.", "reasoning": "Very low complexity. This is a verification and minor bug-fixing task. It involves checking existing code for correctness rather than implementing new features."}, {"taskId": 38, "taskTitle": "前端：集成图表库并创建审核趋势图", "complexityScore": 6, "recommendedSubtasks": 4, "expansionPrompt": "Expand the task '前端：集成图表库并创建审核趋势图' into 4 subtasks: 1. Backend: Create a new API endpoint, e.g., `GET /api/stats/review-trends`, that returns data aggregated by time (e.g., counts per day for the last month). 2. Frontend: Install `echarts` and `vue-echarts` and register the `v-chart` component globally or locally. 3. Frontend: On the statistics page, call the new API to fetch the trend data. 4. Frontend: Use the fetched data to configure an ECharts line chart, setting the `option` object with the correct `xAxis` (time), `yAxis` (count), and `series` data.", "reasoning": "Medium complexity. This involves full-stack work: a new backend endpoint for data aggregation and frontend work to integrate and configure a major new library (ECharts), which has a learning curve."}, {"taskId": 39, "taskTitle": "前端：开发审核分布图表", "complexityScore": 4, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task '前端：开发审核分布图表' into 3 subtasks: 1. Backend: Create a new API endpoint, e.g., `GET /api/stats/status-distribution`, that returns the count of contracts for each status (pending, approved, rejected). 2. Frontend: On the statistics page, call this new API to fetch the distribution data. 3. Frontend: Use the fetched data to configure an ECharts pie chart, setting the `series` data with `name` and `value` pairs for each status.", "reasoning": "Low complexity. This task builds directly on the previous one. With the chart library already integrated, it only requires a new simple backend endpoint and a new chart configuration on the frontend."}, {"taskId": 40, "taskTitle": "管理员功能：开发用户管理页面基础框架", "complexityScore": 4, "recommendedSubtasks": 3, "expansionPrompt": "Expand the task '管理员功能：开发用户管理页面基础框架' into 3 subtasks: 1. Create a new `UserManagement.vue` component and add a route for it (e.g., `/admin/users`), ensuring it is protected by the admin role guard. 2. In the component's `setup` or `mounted` hook, call the API endpoint to fetch the list of all users (`GET /api/admin/users`). 3. Use an `el-table` component to render the fetched user data, displaying columns for username, role, status, and creation date.", "reasoning": "Low complexity. This is a standard 'fetch and display data in a table' task. It's the read-only foundation for a full CRUD page and is straightforward to implement with a component library."}]}