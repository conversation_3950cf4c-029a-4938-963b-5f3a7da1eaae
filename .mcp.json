{"mcpServers": {"task-master-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"GOOGLE_API_KEY": "AIzaSyAcVlQrj0YPuRTEGtB-OzfI5RHiFKm08QE"}}, "Context 7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "serena": {"command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server"]}, "promptx": {"command": "npx", "args": ["-y", "-f", "--registry", "https://registry.npmjs.org", "dpml-prompt@alpha", "mcp-server"]}, "Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"]}, "graphiti-memory": {"url": "http://localhost:8000/sse", "type": "sse"}}}