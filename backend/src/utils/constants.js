/**
 * 系统常量定义
 * 包含用户角色、合同状态、文件配置等常量
 */

// 用户角色
const USER_ROLES = {
  EMPLOYEE: 'employee',           // 员工
  COUNTY_REVIEWER: 'county_reviewer', // 县局审核员
  CITY_REVIEWER: 'city_reviewer',     // 市局审核员
  REVIEWER: 'reviewer',           // 审核员（兼容旧版本）
  ADMIN: 'admin'                  // 超级管理员
};

// 用户状态
const USER_STATUS = {
  ACTIVE: 'active',       // 激活
  BANNED: 'banned'        // 封禁
};

// 合同状态
const CONTRACT_STATUS = {
  PENDING: 'pending',     // 待审核
  APPROVED: 'approved',   // 已通过
  REJECTED: 'rejected'    // 已拒绝
};

// 合同状态流转规则
const STATUS_TRANSITIONS = {
  [CONTRACT_STATUS.PENDING]: [CONTRACT_STATUS.APPROVED, CONTRACT_STATUS.REJECTED],
  [CONTRACT_STATUS.REJECTED]: [CONTRACT_STATUS.PENDING], // 可重新提交
  [CONTRACT_STATUS.APPROVED]: [] // 终态，不可变更
};

// 合同状态中文映射
const CONTRACT_STATUS_TEXT = {
  [CONTRACT_STATUS.PENDING]: '待审核',
  [CONTRACT_STATUS.APPROVED]: '已通过',
  [CONTRACT_STATUS.REJECTED]: '已拒绝'
};

// 用户角色中文映射
const USER_ROLE_TEXT = {
  [USER_ROLES.EMPLOYEE]: '员工',
  [USER_ROLES.COUNTY_REVIEWER]: '县局审核员',
  [USER_ROLES.CITY_REVIEWER]: '市局审核员',
  [USER_ROLES.REVIEWER]: '审核员',
  [USER_ROLES.ADMIN]: '管理员'
};

// 文件配置
const FILE_CONFIG = {
  // 允许的文件类型
  ALLOWED_TYPES: ['.pdf'],
  ALLOWED_MIME_TYPES: ['application/pdf'],

  // 文件大小限制 - 设置为很大的值（1GB），实际上不限制
  MAX_SIZE: 1024 * 1024 * 1024,

  // 上传目录
  UPLOAD_DIR: 'uploads',

  // 文件命名规则 - 支持中文、英文、数字、常用符号
  FILENAME_PATTERN: /^[\w\u4e00-\u9fa5\-. ()（）【】\[\]：:，,；;！!？?""''《》〈〉]+$/
};

// API 响应状态码
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500
};

// API 响应消息
const RESPONSE_MESSAGES = {
  // 成功消息
  SUCCESS: '操作成功',
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出成功',
  CREATE_SUCCESS: '创建成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',
  UPLOAD_SUCCESS: '上传成功',
  SUBMIT_SUCCESS: '提交成功',
  REVIEW_SUCCESS: '审核完成',

  // 错误消息
  INVALID_CREDENTIALS: '用户名或密码错误',
  UNAUTHORIZED: '未授权访问',
  FORBIDDEN: '权限不足',
  NOT_FOUND: '资源不存在',
  USER_NOT_FOUND: '用户不存在',
  CONTRACT_NOT_FOUND: '合同不存在',
  FILE_NOT_FOUND: '文件不存在',

  // 验证错误
  VALIDATION_ERROR: '数据验证失败',
  REQUIRED_FIELD: '必填字段不能为空',
  INVALID_FORMAT: '数据格式不正确',
  DUPLICATE_USERNAME: '用户名已存在',
  DUPLICATE_SERIAL: '流水号已存在',

  // 文件错误
  FILE_TOO_LARGE: '文件大小超过限制',
  INVALID_FILE_TYPE: '不支持的文件类型',
  UPLOAD_FAILED: '文件上传失败',

  // 业务逻辑错误
  INVALID_STATUS_TRANSITION: '无效的状态变更',
  CANNOT_MODIFY_CONTRACT: '当前状态下不允许修改合同',
  CANNOT_REVIEW_OWN_CONTRACT: '不能审核自己提交的合同',
  CONTRACT_ALREADY_REVIEWED: '合同已被审核',
  USER_BANNED: '用户已被封禁',

  // 系统错误
  INTERNAL_ERROR: '系统内部错误',
  DATABASE_ERROR: '数据库操作失败',
  SERVER_ERROR: '服务器错误'
};

// JWT 配置
const JWT_CONFIG = {
  SECRET: process.env.JWT_SECRET || 'hetong-demo-secret-key',
  EXPIRES_IN: '24h',
  ALGORITHM: 'HS256'
};

// 分页配置
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100
};

// 权限配置
const PERMISSIONS = {
  // 合同权限
  CONTRACT_CREATE: 'contract:create',
  CONTRACT_READ: 'contract:read',
  CONTRACT_UPDATE: 'contract:update',
  CONTRACT_DELETE: 'contract:delete',
  CONTRACT_REVIEW: 'contract:review',

  // 用户权限
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  USER_MANAGE: 'user:manage',

  // 系统权限
  SYSTEM_STATS: 'system:stats',
  SYSTEM_MANAGE: 'system:manage'
};

// 角色权限映射（兼容旧版本，新版本使用数据库RBAC）
const ROLE_PERMISSIONS = {
  [USER_ROLES.EMPLOYEE]: [
    PERMISSIONS.CONTRACT_CREATE,
    PERMISSIONS.CONTRACT_READ,
    PERMISSIONS.CONTRACT_UPDATE,
    PERMISSIONS.CONTRACT_DELETE  // 员工可以删除自己的待审核合同
  ],
  [USER_ROLES.COUNTY_REVIEWER]: [
    PERMISSIONS.CONTRACT_READ,
    PERMISSIONS.CONTRACT_REVIEW
  ],
  [USER_ROLES.CITY_REVIEWER]: [
    PERMISSIONS.CONTRACT_READ,
    PERMISSIONS.CONTRACT_REVIEW
  ],
  [USER_ROLES.REVIEWER]: [
    PERMISSIONS.CONTRACT_READ,
    PERMISSIONS.CONTRACT_REVIEW
  ],
  [USER_ROLES.ADMIN]: [
    PERMISSIONS.CONTRACT_CREATE,
    PERMISSIONS.CONTRACT_READ,
    PERMISSIONS.CONTRACT_UPDATE,
    PERMISSIONS.CONTRACT_DELETE,
    PERMISSIONS.CONTRACT_REVIEW,
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.SYSTEM_STATS,
    PERMISSIONS.SYSTEM_MANAGE
  ]
};

// 默认密码
const DEFAULT_PASSWORDS = {
  ADMIN: 'admin123',
  USER: '123456'
};

// 流水号配置
const SERIAL_NUMBER = {
  PREFIX: 'HT',
  LENGTH: 3,
  PATTERN: /^HT\d{3}$/
};

// 系统配置
const SYSTEM_CONFIG = {
  APP_NAME: '合同审核系统',
  VERSION: '1.0.0',
  DESCRIPTION: '合同审核系统 DEMO版',

  // 服务器配置
  PORT: process.env.PORT || 3000,
  HOST: process.env.HOST || 'localhost',

  // 环境配置
  NODE_ENV: process.env.NODE_ENV || 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development'
};

// 导出所有常量
module.exports = {
  USER_ROLES,
  USER_STATUS,
  CONTRACT_STATUS,
  STATUS_TRANSITIONS,
  CONTRACT_STATUS_TEXT,
  USER_ROLE_TEXT,
  FILE_CONFIG,
  HTTP_STATUS,
  RESPONSE_MESSAGES,
  JWT_CONFIG,
  PAGINATION,
  PERMISSIONS,
  ROLE_PERMISSIONS,
  DEFAULT_PASSWORDS,
  SERIAL_NUMBER,
  SYSTEM_CONFIG
};
